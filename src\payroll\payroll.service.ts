import { BadRequestException, Injectable } from '@nestjs/common';
import { AuthorizationQueue } from '@prisma/client';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { ACTIONS_CONSTANT } from 'src/authorization-queue/constants/entity.constants';
import { MODULE_CONSTANT } from 'src/authorization-queue/constants/module.constant';
import { AuthorizationRequestMaker } from 'src/common/maker/authorization-request.maker';
import { DatabaseService } from 'src/database/database.service';
import { PayrollRecordDto } from './dto/create-payroll-record.dto';
import { CreatePayrollUploadDto } from './dto/create-payroll-update.dto';

@Injectable()
export class PayrollService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly authTokenService: AuthTokenService,
    private readonly authorizationRequestMaker: AuthorizationRequestMaker,
  ) {}

  async findPayrollUploadById({ id }: { id: string }) {
    const payrollUpload = await this.databaseService.payrollUpload.findUnique({
      where: {
        id,
      },
    });

    return payrollUpload;
  }
  async findPayrollUpload({
    identifier,
    token,
    period,
  }: {
    identifier: string;
    token: string;
    period?: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    const payrollUpload = await this.databaseService.payrollUpload.findFirst({
      where: {
        OR: [
          { id: identifier },
          {
            period,
            companyId: decodedToken.companyId,
          },
        ],
      },
    });

    return payrollUpload;
  }
  // Method to find a role by ID or Name
  async getPayrollUploads(token: string) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    try {
      const { companyId } = decodedToken;
      const payrollUploads = await this.databaseService.payrollUpload.findMany({
        where: {
          companyId,
        },
        include: {
          branch: {
            select: {
              name: true,
            },
          },
        },
      });

      // await this.databaseService.payrollUpload.findMany({
      //   where: {
      //     companyId,
      //     period: '2025-06',
      //   },
      // });

      return payrollUploads.map((payrollUpload) => ({
        ...payrollUpload,
        branch: payrollUpload.branch?.name.split('|')[1],
      }));
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  // Method to find a role by ID or Name
  async getPayrollRecordByPayrollUploadId(payrollUploadId: string) {
    try {
      const payrollRecords =
        await this.databaseService.payrollRecordUpload.findMany({
          where: {
            payrollId: payrollUploadId,
          },
        });

      return payrollRecords;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async getPayrollBatchByPayrollUploadId(payrollUploadId: string) {
    try {
      const payrollRecords =
        await this.databaseService.payrollUploadTemp.findMany({
          where: {
            payrollUploadId,
          },
        });

      return payrollRecords;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  // Method to create a new role
  async acceptPayrollAction({
    queue,
    approvedBy,
  }: {
    queue: AuthorizationQueue;
    approvedBy: string;
  }) {
    const { requestedBy, action, companyId } = queue;
    switch (action) {
      case 'CREATE': {
        const { period, records, payDate } = JSON.parse(
          queue.data,
        ) as CreatePayrollUploadDto;

        await this.databaseService.$transaction(async (tx) => {
          const newPayrollUpload = await tx.payrollUpload.create({
            data: {
              period,
              uploadedBy: requestedBy,
              companyId,
              payDate,
              approvedBy,
            },
          });

          const payrollRecords = records.map((record) => ({
            payload: JSON.stringify(record),
            payrollUploadId: newPayrollUpload.id,
          }));

          await tx.payrollUploadTemp.createMany({
            data: payrollRecords,
            skipDuplicates: true,
          });
        });

        return true;
      }

      default:
        return false;
    }
  }

  async createPayrollRecord({ payload }: { payload: PayrollRecordDto }) {
    if (!payload.payrollUploadId)
      throw new BadRequestException('Missing Payroll upload id');

    const payrollUploadExist = await this.findPayrollUploadById({
      id: payload.payrollUploadId,
    });

    if (!payrollUploadExist)
      throw new BadRequestException('Payroll upload record not found');

    const staffExist = await this.databaseService.employee.findUnique({
      where: {
        staffCode: `${payrollUploadExist.companyId}|${payload.staffCode}`,
      },
    });

    console.log('Stff', staffExist);

    if (!staffExist)
      throw new BadRequestException(
        `Staff with the code ${payload.staffCode} not found`,
      );

    await this.databaseService.payrollRecordUpload.create({
      data: {
        ...payload,
        staffCode: staffExist.staffCode,
        payrollId: payrollUploadExist.id,
      },
    });

    return true;
  }

  async createPayroll({
    payload,
    token,
  }: {
    payload: CreatePayrollUploadDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    try {
      return await this.authorizationRequestMaker.queueRequest({
        payload: payload,
        action: ACTIONS_CONSTANT.create,
        companyId: decodedToken.companyId,
        module: MODULE_CONSTANT.PAYROLL,
        requestedBy: decodedToken.name || decodedToken.email,
      });
    } catch (error) {
      console.log(error);

      throw new Error('Something went wrong. Try again!');
    }
  }
}

import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsEmail,
  IsInt,
  IsOptional,
  IsString,
} from 'class-validator';

export class UpdateUserDto {
  @ApiProperty()
  @IsString()
  id: string;

  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsEmail()
  email: string;

  @ApiProperty()
  @IsString()
  branches: string[];

  @ApiProperty()
  @IsInt()
  roleId: number;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  twoFactorEnabled?: boolean;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  hasAccessToAllBranches?: boolean;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  isRoot?: boolean;
}

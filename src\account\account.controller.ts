import { Body, Controller, Get, Param, Post } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { SkipThrottle, Throttle } from '@nestjs/throttler';
import { Public } from 'src/common/decorators/public.decorator';
import { SwaggerService } from 'src/swagger/swagger.service';
import { AccountService } from './account.service';
import { CreateAccountDto } from './dto/create-account.dto';

@ApiTags('Account')
@SkipThrottle()
@Controller('account')
export class AccountController {
  constructor(private readonly accountService: AccountService) {}
  @SwaggerService.applyOperation({
    method: 'create',
    description: 'Creates a new company with the provided details.',
    dto: CreateAccountDto,
    statusCodes: [],
  })
  @Throttle({ short: { ttl: 6000, limit: 5 } }) // Allow 5 requests per minute
  @Post()
  @Public()
  createCompany(@Body() payload: CreateAccountDto) {
    return this.accountService.createAccount(payload);
  }

  @SwaggerService.applyOperation({
    method: 'get',
    description: 'Get company details.',
    params: [
      {
        name: 'slug',
        description: 'The slug of the company.',
        type: String,
        example: 'al-halal-holdings',
      },
    ],
    statusCodes: [],
  })
  @Throttle({ short: { ttl: 6000, limit: 10 } }) // Allow 5 requests per minute
  @Get('/:slug')
  @Public()
  getCompanyData(@Param('slug') slug: string) {
    return this.accountService.getAccountData(slug);
  }
}

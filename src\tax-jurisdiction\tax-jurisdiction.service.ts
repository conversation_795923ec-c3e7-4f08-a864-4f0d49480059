import { BadRequestException, Injectable } from '@nestjs/common';
import { AuthorizationQueue } from '@prisma/client';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { ACTIONS_CONSTANT } from 'src/authorization-queue/constants/entity.constants';
import { MODULE_CONSTANT } from 'src/authorization-queue/constants/module.constant';
import { AuthorizationRequestMaker } from 'src/common/maker/authorization-request.maker';
import { DatabaseService } from 'src/database/database.service';
import { CreateBulkTaxJurisdictionDto } from './dto/create-bulk-tax-jurisdiction.dto';
import { CreateTaxJurisdictionDto } from './dto/create-tax-jurisdiction.dto';
import { DeleteTaxJurisdictionDto } from './dto/delete-tax-jurisdiction.dto';
import { UpdateTaxJurisdictionDto } from './dto/update-tax-jurisdiction.dto';

@Injectable()
export class TaxJurisdictionService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly authTokenService: AuthTokenService,
    private readonly authorizationRequestMaker: AuthorizationRequestMaker,
  ) {}

  async findTaxJurisdictionByName(name: string) {
    const taxJurisdiction =
      await this.databaseService.taxJurisdiction.findUnique({
        where: {
          name,
        },
      });

    return taxJurisdiction;
  }

  async findTaxJurisdiction({
    identifier,
    token,
  }: {
    identifier: string;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    const taxJurisdiction =
      await this.databaseService.taxJurisdiction.findFirst({
        where: {
          OR: [
            { id: identifier },
            {
              name: `${decodedToken.companyId}|${identifier}`,
              companyId: decodedToken.companyId,
            },
          ],
        },
      });

    return taxJurisdiction;
  }

  async getTaxJurisdictions(token: string) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    console.log(decodedToken);

    try {
      const { companyId } = decodedToken;
      const taxJurisdictions =
        await this.databaseService.taxJurisdiction.findMany({
          where: {
            companyId,
          },
        });

      return taxJurisdictions.map((taxJurisdiction) => ({
        ...taxJurisdiction,
        name: taxJurisdiction.name.split('|')[1],
      }));
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async acceptTaxJurisdictionAction({
    queue,
    approvedBy,
  }: {
    queue: AuthorizationQueue;
    approvedBy: string;
  }) {
    const { requestedBy, action, companyId } = queue;
    switch (action as ACTIONS_CONSTANT) {
      case ACTIONS_CONSTANT.create: {
        const { name, description } = JSON.parse(
          queue.data,
        ) as CreateTaxJurisdictionDto;

        // Check if the group already exists by name
        const taxJurisdictionExist =
          await this.databaseService.taxJurisdiction.findFirst({
            where: {
              companyId,
              name,
            },
          });

        if (taxJurisdictionExist) {
          throw new BadRequestException('TaxJurisdiction already exists');
        }

        await this.databaseService.taxJurisdiction.create({
          data: {
            name: `${companyId}|${name}`,
            description: description || name,
            companyId,
            createdBy: requestedBy,
            approvedBy,
          },
        });

        return true;
      }

      case ACTIONS_CONSTANT.bulk_create: {
        const parsed = JSON.parse(queue.data) as CreateBulkTaxJurisdictionDto;

        // If it's a bulk request
        if (Array.isArray(parsed.items)) {
          const results: { name: string; status: string }[] = [];

          for (const item of parsed.items) {
            const name = `${companyId}|${item.name}`;

            const exists = await this.databaseService.taxJurisdiction.findFirst(
              {
                where: {
                  companyId,
                  name,
                },
              },
            );

            if (exists) {
              results.push({ name: item.name, status: 'skipped' });
              continue;
            }

            await this.databaseService.taxJurisdiction.create({
              data: {
                name,
                description: item.description || item.name,
                companyId,
                createdBy: requestedBy,
                approvedBy,
              },
            });

            results.push({ name: item.name, status: 'created' });
          }

          return true;
        }

        return true;
      }

      case ACTIONS_CONSTANT.update: {
        const payload = JSON.parse(queue.data) as UpdateTaxJurisdictionDto;

        await this.updateTaxJurisdiction({
          companyId,
          payload,
        });

        return true;
      }

      case ACTIONS_CONSTANT.delete: {
        const payload = JSON.parse(queue.data) as DeleteTaxJurisdictionDto;

        await this.deleteTaxJurisdiction({
          companyId,
          payload,
        });

        return true;
      }

      default:
        return false;
    }
  }

  async createTaxJurisdiction({
    payload,
    token,
  }: {
    payload: CreateTaxJurisdictionDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    try {
      await this.databaseService.authorizationQueue.create({
        data: {
          data: JSON.stringify(payload),
          action: 'CREATE',
          companyId: decodedToken.companyId,
          module: MODULE_CONSTANT.TAX_JURISDICTION,
          requestedBy: decodedToken.name,
        },
      });
      return;
    } catch (error) {
      console.log(error);

      throw new Error('Something went wrong. Try again!');
    }
  }

  async updateTaxJurisdictionRequest({
    payload,
    token,
  }: {
    payload: UpdateTaxJurisdictionDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    try {
      return await this.authorizationRequestMaker.queueRequest({
        payload,
        action: ACTIONS_CONSTANT.update,
        companyId: decodedToken.companyId,
        module: MODULE_CONSTANT.TAX_JURISDICTION,
        requestedBy: decodedToken.name,
      });
      return;
    } catch (error) {
      console.log(error);

      throw new Error('Something went wrong. Try again!');
    }
  }

  async updateTaxJurisdiction({
    payload,
    companyId,
  }: {
    payload: UpdateTaxJurisdictionDto;
    companyId: string;
  }) {
    const { description, name, id } = payload;

    try {
      if (!id) {
        throw new BadRequestException('Tax Jurisdiction Id is required.');
      }

      const taxJurisdictionExist =
        await this.databaseService.taxJurisdiction.findUnique({
          where: {
            id,
            companyId,
          },
        });

      if (!taxJurisdictionExist) {
        throw new BadRequestException('TaxJurisdiction not found.');
      }

      await this.databaseService.taxJurisdiction.update({
        where: {
          id: taxJurisdictionExist.id,
        },
        data: {
          name: name ? `${companyId}|${name}` : taxJurisdictionExist.name,
          companyId,
          description: description ? description : name,
        },
      });

      return;
    } catch (error) {
      console.log('Error updating taxJurisdiction:', error);

      throw error;
    }
  }

  async deleteTaxJurisdictionRequest({
    payload,
    token,
  }: {
    payload: DeleteTaxJurisdictionDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    try {
      return await this.authorizationRequestMaker.queueRequest({
        payload,
        action: ACTIONS_CONSTANT.delete,
        companyId: decodedToken.companyId,
        module: MODULE_CONSTANT.TAX_JURISDICTION,
        requestedBy: decodedToken.name,
      });
    } catch (error) {
      console.log(error);

      throw new Error('Something went wrong. Try again!');
    }
  }

  async deleteTaxJurisdiction({
    payload,
    companyId,
  }: {
    payload: DeleteTaxJurisdictionDto;
    companyId: string;
  }) {
    const { id } = payload;

    try {
      if (!id) {
        throw new BadRequestException('Tax Jurisdiction is required.');
      }

      const taxJurisdictionExist =
        await this.databaseService.taxJurisdiction.findUnique({
          where: {
            id,
            companyId,
          },
        });

      if (!taxJurisdictionExist) {
        throw new BadRequestException('Tax Jurisdiction not found.');
      }

      await this.databaseService.taxJurisdiction.update({
        where: {
          id: taxJurisdictionExist.id,
        },
        data: {
          status: 'INACTIVE',
        },
      });

      return;
    } catch (error) {
      console.log('Error deleting taxJurisdiction:', error);

      throw error;
    }
  }

  async createBulkTaxJurisdictions({
    payload,
    token,
  }: {
    payload: CreateBulkTaxJurisdictionDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);
    const { companyId, name: requestedBy } = decodedToken;

    if (!payload || payload.items.length <= 0) {
      throw new BadRequestException('Missing required data');
    }

    try {
      await this.databaseService.authorizationQueue.create({
        data: {
          data: JSON.stringify(payload), // will contain the entire items array
          action: ACTIONS_CONSTANT.bulk_create,
          companyId,
          module: MODULE_CONSTANT.TAX_JURISDICTION,
          requestedBy,
        },
      });

      return;
    } catch (error) {
      console.error(error);
      throw new Error('Bulk creation failed. Try again.');
    }
  }
}

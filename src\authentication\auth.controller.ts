import { Body, Controller, Headers, Post } from '@nestjs/common';
import { ApiBody, ApiTags } from '@nestjs/swagger';
import { Public } from 'src/common/decorators/public.decorator';
import { ResendOtpDto } from 'src/otp/dto/resend-otp.dto';
import { OtpService } from 'src/otp/otp.service';
import { AuthService } from './auth.service';
import { AuthChangePasswordDto } from './dto/auth-change-password.dto';
import { AuthCompleteForgotPasswordDto } from './dto/auth-complete-forgot-password.dto';
import { AuthInitiateForgotPasswordDto } from './dto/auth-initiate-forgot-password.dto';
import { AuthSwitchDto } from './dto/auth-switch.dto';
import { AuthUserDto } from './dto/auth-user.dto';

@ApiTags('Auth')
@Controller('auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly otpService: OtpService,
  ) {}
  @Public()
  @ApiBody({ type: AuthUserDto })
  @Post('login')
  auth(@Body() payload: AuthUserDto) {
    return this.authService.signIn(payload);
  }

  @Public()
  @ApiBody({ type: AuthInitiateForgotPasswordDto })
  @Post('initiate/forgot-password')
  initiateForgotPassword(@Body() payload: AuthInitiateForgotPasswordDto) {
    return this.authService.initiateForgotPassword(payload);
  }

  @Public()
  @ApiBody({ type: AuthCompleteForgotPasswordDto })
  @Post('complete/forgot-password')
  completeForgotPassword(@Body() payload: AuthCompleteForgotPasswordDto) {
    return this.authService.completeForgotPassword(payload);
  }

  @ApiBody({ type: AuthSwitchDto })
  @Post('switch-comapny')
  switchCompany(
    @Body() payload: AuthSwitchDto,
    @Headers('authorization') authHeader: string,
  ) {
    const token = authHeader?.split(' ')[1];

    return this.authService.switchCompany({ payload, token });
  }

  @Public()
  @ApiBody({ type: ResendOtpDto })
  @Post('resend-otp')
  resendOtp(@Body() payload: ResendOtpDto) {
    return this.otpService.resendOtp(payload);
  }

  @Post('change-password')
  async changePassword(
    @Body() payload: AuthChangePasswordDto,
    @Headers('authorization') authHeader: string,
  ) {
    const token = authHeader?.split(' ')[1];

    return this.authService.changePassword({ payload, token });
  }
}

import { Module } from '@nestjs/common';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { DatabaseModule } from 'src/database/database.module';
import { LocationController } from './location.controller';
import { LocationService } from './location.service';

@Module({
  imports: [DatabaseModule],
  providers: [LocationService, AuthTokenService],
  controllers: [LocationController],
})
export class LocationModule {}

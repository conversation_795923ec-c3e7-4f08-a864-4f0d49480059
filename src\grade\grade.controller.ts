import { Body, Controller, Get, Post, Req } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { RequirePrivilege } from 'src/common/decorators/require-privilege.decorator';
import { SwaggerService } from 'src/swagger/swagger.service';
import { CreateGradeDto } from './dto/create-grade.dto';
import { UpdateGradeDto } from './dto/update-grade.dto';
import { GradeService } from './grade.service';

@ApiTags('Grade')
@Controller('grade')
export class GradeController {
  constructor(private readonly gradeService: GradeService) {}
  @SwaggerService.applyOperation({
    method: 'create',
    description: 'Creates a new grade with the provided details.',
    dto: CreateGradeDto,
    statusCodes: [],
  })
  @RequirePrivilege('GRADE|CREATE')
  @Post()
  createGrade(@Body() createGradeDto: CreateGradeDto, @Req() request: Request) {
    const token = request.headers.authorization?.split(' ')[1];

    return this.gradeService.createGrade({
      payload: createGradeDto,
      token: token!,
    });
  }

  @SwaggerService.applyOperation({
    method: 'get',
    description: 'Get all grades.',
    statusCodes: [],
  })
  @RequirePrivilege('GRADE|VIEW')
  @Get()
  getGrades(@Req() request: Request) {
    const token = request.headers.authorization?.split(' ')[1];

    return this.gradeService.getGrade(token!);
  }

  @SwaggerService.applyOperation({
    method: 'update',
    description: 'Update a grade.',
    statusCodes: [],
  })
  @RequirePrivilege('GRADE|UPDATE')
  @Post('/update')
  updateGrade(@Req() request: Request, @Body() payload: UpdateGradeDto) {
    const token = request.headers.authorization?.split(' ')[1];

    return this.gradeService.updateGradeRequest({ payload, token: token! });
  }

  @SwaggerService.applyOperation({
    method: 'delete',
    description: 'Delete a grade.',
    statusCodes: [],
  })
  @RequirePrivilege('GRADE|UPDATE')
  @Post('/delete')
  deleteGrade(@Req() request: Request, @Body() payload: UpdateGradeDto) {
    const token = request.headers.authorization?.split(' ')[1];

    return this.gradeService.deleteGradeRequest({ payload, token: token! });
  }
}

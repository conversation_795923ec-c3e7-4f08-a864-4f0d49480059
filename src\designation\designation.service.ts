import { BadRequestException, Injectable } from '@nestjs/common';
import { AuthorizationQueue } from '@prisma/client';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { ACTIONS_CONSTANT } from 'src/authorization-queue/constants/entity.constants';
import { MODULE_CONSTANT } from 'src/authorization-queue/constants/module.constant';
import { AuthorizationRequestMaker } from 'src/common/maker/authorization-request.maker';
import { DatabaseService } from 'src/database/database.service';
import { DeleteDesignationDto } from './dto/delete-designation.dto';
import { CreateDesignationDto } from './dto/designation.dto';
import { UpdateDesignationDto } from './dto/update-designation.dto';

@Injectable()
export class DesignationService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly authTokenService: AuthTokenService,
    private readonly authorizationRequestMaker: AuthorizationRequestMaker,
  ) {}

  async findDesignation({
    identifier,
    token,
  }: {
    identifier: string;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    const designation = await this.databaseService.jobTitle.findFirst({
      where: {
        OR: [
          { id: identifier },
          {
            name: `${decodedToken.companyId}|${identifier}`,
            companyId: decodedToken.companyId,
          },
        ],
      },
    });

    return designation;
  }
  // Method to find a role by ID or Name
  async getDesignations(token: string) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    try {
      const { companyId } = decodedToken;
      const designations = await this.databaseService.jobTitle.findMany({
        where: {
          companyId,
        },
        include: {
          _count: {
            select: {
              employee: true,
            },
          },
        },
      });

      return designations.map((designation) => ({
        ...designation,
        name: designation.name.split('|')[1],
      }));
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  // Method to create a new role
  async acceptDesignationAction({
    queue,
    approvedBy,
  }: {
    queue: AuthorizationQueue;
    approvedBy: string;
  }) {
    const { requestedBy, action, companyId } = queue;
    switch (action as unknown as ACTIONS_CONSTANT) {
      case ACTIONS_CONSTANT.create: {
        const { name, description } = JSON.parse(
          queue.data,
        ) as CreateDesignationDto;

        // Check if the group already exists by name
        const designationExist = await this.databaseService.jobTitle.findUnique(
          {
            where: {
              name: `${companyId}|${name}`,
            },
          },
        );

        if (designationExist) {
          throw new BadRequestException('Designation already exists');
        }

        await this.databaseService.jobTitle.create({
          data: {
            name: `${companyId}|${name}`,
            description: description || name,
            companyId,
            createdBy: requestedBy,
            approvedBy,
          },
        });

        return true;
      }

      case ACTIONS_CONSTANT.update: {
        const payload = JSON.parse(queue.data) as UpdateDesignationDto;

        await this.updateDesignation({
          companyId,
          payload,
        });

        return true;
      }

      case ACTIONS_CONSTANT.delete: {
        const payload = JSON.parse(queue.data) as DeleteDesignationDto;

        await this.deleteDesignation({
          companyId,
          payload,
        });

        return true;
      }
      default:
        return false;
    }
  }

  async createDesignation({
    payload,
    token,
  }: {
    payload: CreateDesignationDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    try {
      return await this.authorizationRequestMaker.queueRequest({
        payload,
        action: ACTIONS_CONSTANT.create,
        companyId: decodedToken.companyId,
        module: MODULE_CONSTANT.DESIGNATION,
        requestedBy: decodedToken.name || decodedToken.email,
      });
    } catch (error) {
      console.log(error);

      throw new Error('Something went wrong. Try again!');
    }
  }

  async updateDesignation({
    payload,
    companyId,
  }: {
    payload: UpdateDesignationDto;
    companyId: string;
  }) {
    const { description, name, id } = payload;

    try {
      if (!id) {
        throw new BadRequestException('Designation Id is required.');
      }

      const designationExist = await this.databaseService.jobTitle.findUnique({
        where: {
          id,
          companyId,
        },
      });

      if (!designationExist) {
        throw new BadRequestException('Designation not found.');
      }

      await this.databaseService.jobTitle.update({
        where: {
          id: designationExist.id,
        },
        data: {
          name: name ? `${companyId}|${name}` : designationExist.name,
          description: description ? description : name,
        },
      });

      return;
    } catch (error) {
      console.log('Error updating designation:', error);

      throw error;
    }
  }

  async deleteDesignation({
    payload,
    companyId,
  }: {
    payload: DeleteDesignationDto;
    companyId: string;
  }) {
    const { id } = payload;

    try {
      if (!id) {
        throw new BadRequestException('Designation Id is required.');
      }

      const designationExist = await this.databaseService.jobTitle.findUnique({
        where: {
          id,
          companyId,
        },
      });

      if (!designationExist) {
        throw new BadRequestException('Designation not found.');
      }

      await this.databaseService.jobTitle.update({
        where: {
          id: designationExist.id,
        },
        data: {
          status: 'INACTIVE',
        },
      });

      return;
    } catch (error) {
      console.log('Error deleting designation', error);

      throw error;
    }
  }

  async queueRequest({
    payload,
    companyId,
    action,
    module,
    requestedBy,
  }: {
    payload: any;
    companyId: string;
    module: MODULE_CONSTANT;
    action: ACTIONS_CONSTANT;
    requestedBy: string;
  }) {
    try {
      await this.databaseService.authorizationQueue.create({
        data: {
          data: JSON.stringify(payload),
          action,
          companyId,
          module,
          requestedBy,
        },
      });
      return { message: 'Request successful and pending authorztion' };
    } catch (error) {
      console.log(error);

      throw new Error('Something went wrong. Try again!');
    }
  }

  async deleteDesignationRequest({
    payload,
    token,
  }: {
    payload: DeleteDesignationDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    try {
      return await this.authorizationRequestMaker.queueRequest({
        payload,
        action: ACTIONS_CONSTANT.delete,
        companyId: decodedToken.companyId,
        module: MODULE_CONSTANT.DESIGNATION,
        requestedBy: decodedToken.name || decodedToken.email,
      });
    } catch (error) {
      console.log(error);

      throw new Error('Something went wrong. Try again!');
    }
  }

  async updateDesignationRequest({
    payload,
    token,
  }: {
    payload: UpdateDesignationDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    try {
      return await this.authorizationRequestMaker.queueRequest({
        payload,
        action: ACTIONS_CONSTANT.update,
        companyId: decodedToken.companyId,
        module: MODULE_CONSTANT.DESIGNATION,
        requestedBy: decodedToken.name || decodedToken.email,
      });
    } catch (error) {
      console.log(error);

      throw new Error('Something went wrong. Try again!');
    }
  }
}

import { BadRequestException, Injectable } from '@nestjs/common';
import { AuthorizationQueue } from '@prisma/client';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { ACTIONS_CONSTANT } from 'src/authorization-queue/constants/entity.constants';
import { MODULE_CONSTANT } from 'src/authorization-queue/constants/module.constant';
import { AuthorizationRequestMaker } from 'src/common/maker/authorization-request.maker';
import { DatabaseService } from 'src/database/database.service';
import { CreateGradeDto } from './dto/create-grade.dto';
import { DeleteGradeDto } from './dto/delete-grade.dto';
import { UpdateGradeDto } from './dto/update-grade.dto';

@Injectable()
export class GradeService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly authTokenService: AuthTokenService,
    private readonly authorizationRequestMaker: AuthorizationRequestMaker,
  ) {}

  async findGrade({
    identifier,
    token,
  }: {
    identifier: string;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    const grade = await this.databaseService.jobGrade.findFirst({
      where: {
        OR: [
          { id: identifier },
          {
            name: `${decodedToken.companyId}|${identifier}`,
            companyId: decodedToken.companyId,
          },
        ],
      },
    });

    return grade;
  }

  async getGrade(token: string) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    console.log(decodedToken);

    try {
      const { companyId } = decodedToken;
      const grades = await this.databaseService.jobGrade.findMany({
        where: {
          companyId,
          status: {
            not: 'INACTIVE',
          },
        },
      });

      console.log(grades);

      return grades.map((grade) => ({
        ...grade,
        name: grade.name.split('|')[1],
      }));
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  // Method to create a new role
  async acceptGradeAction({
    queue,
    approvedBy,
  }: {
    queue: AuthorizationQueue;
    approvedBy: string;
  }) {
    const { requestedBy, action, companyId } = queue;
    switch (action) {
      case 'CREATE': {
        const { name, description } = JSON.parse(queue.data) as CreateGradeDto;

        // Check if the group already exists by name
        const gradeExist = await this.databaseService.jobGrade.findUnique({
          where: {
            name_companyId: {
              companyId,
              name: `${companyId}|${name}`,
            },
          },
        });

        if (gradeExist) {
          throw new BadRequestException('Grade already exists');
        }

        await this.databaseService.jobGrade.create({
          data: {
            name: `${companyId}|${name}`,
            description: description || name,
            companyId,
            createdBy: requestedBy,
            approvedBy,
          },
        });

        return true;
      }

      case 'UPDATE': {
        const payload = JSON.parse(queue.data) as UpdateGradeDto;

        await this.updateGrade({
          companyId,
          payload,
        });

        return true;
      }

      case 'DELETE': {
        const payload = JSON.parse(queue.data) as DeleteGradeDto;

        await this.deleteGrade({
          companyId,
          payload,
        });

        return true;
      }
      default:
        return false;
    }
  }

  async createGrade({
    payload,
    token,
  }: {
    payload: CreateGradeDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    try {
      return await this.authorizationRequestMaker.queueRequest({
        payload,
        action: ACTIONS_CONSTANT.create,
        companyId: decodedToken.companyId,
        module: MODULE_CONSTANT.GRADE,
        requestedBy: decodedToken.name || decodedToken.email,
      });
    } catch (error) {
      console.log(error);

      throw new Error('Something went wrong. Try again!');
    }
  }

  async updateGradeRequest({
    payload,
    token,
  }: {
    payload: UpdateGradeDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    try {
      return await this.authorizationRequestMaker.queueRequest({
        payload,
        action: ACTIONS_CONSTANT.update,
        companyId: decodedToken.companyId,
        module: MODULE_CONSTANT.GRADE,
        requestedBy: decodedToken.name || decodedToken.email,
      });
      return;
    } catch (error) {
      console.log(error);

      throw new Error('Something went wrong. Try again!');
    }
  }

  async updateGrade({
    payload,
    companyId,
  }: {
    payload: UpdateGradeDto;
    companyId: string;
  }) {
    const { description, name, id } = payload;

    try {
      if (!id) {
        throw new BadRequestException('Grade Id is required.');
      }

      const gradeExist = await this.databaseService.jobGrade.findUnique({
        where: {
          id,
          companyId,
        },
      });

      if (!gradeExist) {
        throw new BadRequestException('Grade not found.');
      }

      const updatedGrade = await this.databaseService.jobGrade.update({
        where: {
          id: gradeExist.id,
        },
        data: {
          name: name ? `${companyId}|${name}` : gradeExist.name,
          companyId,
          description: description ? description : name,
        },
      });

      return updatedGrade;
    } catch (error) {
      console.log('Error updating grade:', error);

      throw error;
    }
  }

  async deleteGradeRequest({
    payload,
    token,
  }: {
    payload: DeleteGradeDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    try {
      return await this.authorizationRequestMaker.queueRequest({
        payload,
        action: ACTIONS_CONSTANT.delete,
        companyId: decodedToken.companyId,
        module: MODULE_CONSTANT.GRADE,
        requestedBy: decodedToken.name || decodedToken.email,
      });
      return;
    } catch (error) {
      console.log(error);

      throw new Error('Something went wrong. Try again!');
    }
  }

  async deleteGrade({
    payload,
    companyId,
  }: {
    payload: DeleteGradeDto;
    companyId: string;
  }) {
    const { id } = payload;

    try {
      if (!id) {
        throw new BadRequestException('Grade Id is required.');
      }

      const gradeExist = await this.databaseService.jobGrade.findUnique({
        where: {
          id,
          companyId,
        },
      });

      if (!gradeExist) {
        throw new BadRequestException('Grade not found.');
      }

      const deletedGrade = await this.databaseService.jobGrade.update({
        where: {
          id: gradeExist.id,
        },
        data: {
          status: 'INACTIVE',
        },
      });

      return deletedGrade;
    } catch (error) {
      console.log('Error deleting grade:', error);

      throw error;
    }
  }
}

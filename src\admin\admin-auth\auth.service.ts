import { BadRequestException, Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Account, User } from '@prisma/client';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { CryptoService } from 'src/common/crypto/crypto.service';
import { DatabaseService } from 'src/database/database.service';
import { OtpService } from 'src/otp/otp.service';
import { AdminAuthDto } from './dto/admin-auth.dto';
import { AuthCompleteForgotPasswordDto } from './dto/auth-complete-forgot-password.dto';
import { AuthInitiateForgotPasswordDto } from './dto/auth-initiate-forgot-password.dto';

@Injectable()
export class AdminAuthService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly cryptoService: CryptoService,
    private jwtService: JwtService,
    private readonly authTokenService: AuthTokenService,
    private readonly otpService: OtpService,
  ) {}

  async signIn(payload: AdminAuthDto) {
    const { email, password } = payload;

    try {
      const userExist = await this.databaseService.adminUser.findUnique({
        where: {
          email: email,
        },
      });

      if (!userExist) {
        throw new BadRequestException('Invalid Credentials');
      }

      const isPassword = await this.cryptoService.compare({
        data: password,
        hash: userExist?.password,
      });

      if (!isPassword) {
        throw new BadRequestException('Invalid Credentials');
      }

      // TODO: Generate a JWT and return it here
      const access_token = await this.jwtService.signAsync(
        {
          email: userExist?.email,
          roleId: userExist?.roleId,
          name: userExist?.name || userExist?.email,
        },
        {
          expiresIn: '24h',
        },
      );

      return {
        ...userExist,
        userId: userExist?.id || null,
        password: '',
        access_token,
      };
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async initiateForgotPassword(payload: AuthInitiateForgotPasswordDto) {
    const { email, companyId, accountId } = payload;

    let accountExist: Account | null = null;

    let userExist: User | null = null;

    if (!companyId && !accountId) {
      throw new BadRequestException('Missing required field');
    }

    try {
      if (!companyId) {
        accountExist = await this.databaseService.account.findUnique({
          where: {
            id: accountId,
            email,
          },
        });
      } else {
        userExist = await this.databaseService.user.findUnique({
          where: {
            companyId,
            email,
          },
        });
      }

      if (!accountExist && !userExist) {
        throw new BadRequestException('User account not found');
      }

      const otp = await this.otpService.requestOtp({
        email,
        companyId,
      });

      return {
        otpRef: otp.otpRef,
      };
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async completeForgotPassword(payload: AuthCompleteForgotPasswordDto) {
    const { email, companyId, accountId, otp, password } = payload;

    let accountExist: Account | null = null;

    let userExist: User | null = null;

    if (!companyId && !accountId) {
      throw new BadRequestException('Missing required field');
    }

    try {
      if (!companyId) {
        accountExist = await this.databaseService.account.findUnique({
          where: {
            id: accountId,
            email,
          },
        });
      } else {
        userExist = await this.databaseService.user.findUnique({
          where: {
            companyId,
            email,
          },
        });
      }

      if (!accountExist && !userExist) {
        throw new BadRequestException('User account not found');
      }

      const otpIsValid = await this.otpService.verifyAuthOtp({
        email,
        code: otp,
        companyId,
      });

      if (!otpIsValid) {
        throw new BadRequestException('Inalid or expired OTP');
      }

      const hashedPassword = await this.cryptoService.hash(password);

      if (accountExist) {
        await this.databaseService.account.update({
          where: {
            id: accountExist.id,
          },
          data: {
            password: hashedPassword,
          },
        });
      } else if (userExist) {
        await this.databaseService.user.update({
          where: {
            id: userExist.id,
          },
          data: {
            password: hashedPassword,
          },
        });
      }

      return;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }
}

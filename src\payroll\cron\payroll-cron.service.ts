import { BadRequestException, Injectable, Logger } from '@nestjs/common';

import { Cron, CronExpression } from '@nestjs/schedule';
import { Status } from '@prisma/client';
import { DatabaseService } from 'src/database/database.service';
import { PayrollRecordDto } from '../dto/create-payroll-record.dto';
import { PayrollService } from '../payroll.service';

@Injectable()
export class PayrollCronService {
  private readonly logger = new Logger(PayrollService.name);

  constructor(
    private readonly db: DatabaseService,
    private readonly payrollService: PayrollService,
  ) {}

  async handlePendingBulkJobs() {
    const records = await this.db.payrollUploadTemp.findMany({
      where: { status: Status.PENDING },
      take: 20,
    });

    this.logger.log(`Processing ${records.length} pending payroll records`);

    for (const record of records) {
      try {
        const payrollRecord = await this.payrollService.createPayrollRecord({
          payload: {
            ...(JSON.parse(record.payload) as PayrollRecordDto),
            payrollUploadId: record.payrollUploadId,
          },
        });

        if (payrollRecord) {
          await this.db.payrollUploadTemp.delete({
            where: { id: record.id },
          });
        }
      } catch (error) {
        this.logger.error(`Failed to process record ${record.id}: ${error}`);

        let reason = 'Server error';

        if (error instanceof BadRequestException) {
          const res = error.getResponse();

          if (typeof res === 'string') {
            reason = res;
          } else if (
            typeof res === 'object' &&
            res !== null &&
            'message' in res
          ) {
            const message = (res as { message: string | string[] }).message;
            reason = Array.isArray(message) ? message.join(', ') : message;
          }
        }

        await this.db.payrollUploadTemp.update({
          where: { id: record.id },
          data: {
            status: Status.FAILED,
            failureReason: reason,
          },
        });
      }
    }
  }
  // async finalizeCompletedJobs() {
  //   // 1. Find all jobs that are still PENDING
  //   const pendingJobs = await this.db.bulkEmployeeJob.findMany({
  //     where: { status: Status.PENDING },
  //     include: {
  //       records: true,
  //     },
  //   });

  //   for (const job of pendingJobs) {
  //     // 2. Check if job has any records with status PENDING
  //     const hasPendingRecords = job.records.some(
  //       (record) => record.status === Status.PENDING,
  //     );

  //     // 3. If NO pending records, update job status to COMPLETED
  //     if (!hasPendingRecords) {
  //       await this.db.bulkEmployeeJob.update({
  //         where: { id: job.id },
  //         data: {
  //           status: Status.COMPLETED,
  //         },
  //       });
  //       this.logger.log(`Marked job ${job.id} as COMPLETED`);
  //     }
  //   }
  // }

  @Cron(CronExpression.EVERY_5_MINUTES)
  async handleCron() {
    await this.handlePendingBulkJobs();
  }
}

import { Body, Controller, Get, Param, Post, Req } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { SwaggerService } from 'src/swagger/swagger.service';
import { AllowanceService } from './allowance.service';
import { CreateAllowanceDto } from './dto/create-allowance.dto';

@ApiTags('Allowance')
@Controller('allowance')
export class AllowanceController {
  constructor(private readonly allowanceService: AllowanceService) {}
  @SwaggerService.applyOperation({
    method: 'create',
    description: 'Creates a new allowance with the provided details.',
    dto: CreateAllowanceDto,
    statusCodes: [],
  })
  @Post('/create')
  createAllowance(
    @Body() payload: CreateAllowanceDto,
    @Req() request: Request,
  ) {
    const token = request.headers.authorization?.split(' ')[1];

    return this.allowanceService.createAllowance({
      payload,
      token: token!,
    });
  }

  @SwaggerService.applyOperation({
    method: 'get',
    description: 'Get all allowances.',
    statusCodes: [],
  })
  @Get('/read')
  getAllowances(@Req() request: Request) {
    const token = request.headers.authorization?.split(' ')[1];

    return this.allowanceService.getAllowances(token!);
  }

  @SwaggerService.applyOperation({
    method: 'get',
    description: 'Get all allowances by grade level id.',
    statusCodes: [],
  })
  @Get('/read/by-grade-level/:gradeLevelId')
  getAllowancesByGradeLevelId(
    @Req() request: Request,
    @Param('gradeLevelId') gradeLevelId: string,
  ) {
    const token = request.headers.authorization?.split(' ')[1];

    return this.allowanceService.getAllowancesByGradeLevelId({
      token: token!,
      gradeLevelId,
    });
  }
}

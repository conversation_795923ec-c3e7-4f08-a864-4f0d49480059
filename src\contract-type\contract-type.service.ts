import { BadRequestException, Injectable } from '@nestjs/common';
import { AuthorizationQueue } from '@prisma/client';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { ACTIONS_CONSTANT } from 'src/authorization-queue/constants/entity.constants';
import { MODULE_CONSTANT } from 'src/authorization-queue/constants/module.constant';
import { AuthorizationRequestMaker } from 'src/common/maker/authorization-request.maker';
import { DatabaseService } from 'src/database/database.service';
import { CreateContractTypeDto } from './dto/contract-type-status.dto';
import { DeleteContractTypeDto } from './dto/delete-contract-type.dto';
import { UpdateContractTypeDto } from './dto/update-contract-type.dto';

@Injectable()
export class ContractTypeService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly authTokenService: AuthTokenService,
    private readonly authorizationRequestMaker: AuthorizationRequestMaker,
  ) {}

  async findContractType({
    identifier,
    token,
  }: {
    identifier: string;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    // If identifier is a number, search by id, otherwise by name
    const contractType = await this.databaseService.contractType.findFirst({
      where: {
        OR: [
          { id: identifier },
          {
            name: `${decodedToken.companyId}|${identifier}`,
            companyId: decodedToken.companyId,
          },
        ],
      },
    });

    return contractType;
  }
  // Method to find a role by ID or Name
  async getContractTypes(token: string) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    console.log(decodedToken);

    try {
      const { companyId } = decodedToken;
      const contractTypes = await this.databaseService.contractType.findMany({
        where: {
          companyId,
        },
        include: {
          _count: {
            select: {
              employees: true,
            },
          },
        },
      });

      return contractTypes.map((contractType) => ({
        ...contractType,
        name: contractType.name.split('|')[1],
      }));
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  // Method to create a new role
  async acceptContractTypeAction({
    queue,
    approvedBy,
  }: {
    queue: AuthorizationQueue;
    approvedBy: string;
  }) {
    const { requestedBy, action, companyId } = queue;
    switch (action as ACTIONS_CONSTANT) {
      case ACTIONS_CONSTANT.create: {
        const { name, description } = JSON.parse(
          queue.data,
        ) as CreateContractTypeDto;

        // Check if the group already exists by name
        const contractTypeExist =
          await this.databaseService.contractType.findUnique({
            where: {
              name_companyId: {
                companyId,
                name: `${companyId}|${name}`,
              },
            },
          });

        if (contractTypeExist) {
          throw new BadRequestException('Contract Type already exists');
        }

        await this.databaseService.contractType.create({
          data: {
            name: `${companyId}|${name}`,
            description: description || name,
            companyId,
            createdBy: requestedBy,
            approvedBy,
          },
        });

        return true;
      }

      case ACTIONS_CONSTANT.update: {
        const payload = JSON.parse(queue.data) as UpdateContractTypeDto;

        await this.updateContractType({
          companyId,
          payload,
        });

        return true;
      }

      case ACTIONS_CONSTANT.delete: {
        const payload = JSON.parse(queue.data) as DeleteContractTypeDto;

        await this.deleteContractType({
          companyId,
          payload,
        });

        return true;
      }
      default:
        return false;
    }
  }

  async createContractType({
    payload,
    token,
  }: {
    payload: CreateContractTypeDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    try {
      await this.authorizationRequestMaker.queueRequest({
        payload,
        action: ACTIONS_CONSTANT.create,
        companyId: decodedToken.companyId,
        module: MODULE_CONSTANT.CONTRACT_TYPE,
        requestedBy: decodedToken.name || decodedToken.email,
      });
      return;
    } catch (error) {
      console.log(error);

      throw new Error('Something went wrong. Try again!');
    }
  }

  async updateContractType({
    payload,
    companyId,
  }: {
    payload: UpdateContractTypeDto;
    companyId: string;
  }) {
    const { description, name, id } = payload;

    try {
      if (!id) {
        throw new BadRequestException('Contract type Id is required.');
      }

      const contractTypeExist =
        await this.databaseService.contractType.findUnique({
          where: {
            id,
            companyId,
          },
        });

      if (!contractTypeExist) {
        throw new BadRequestException('Contract type not found.');
      }

      await this.databaseService.contractType.update({
        where: {
          id: contractTypeExist.id,
        },
        data: {
          name: name ? `${companyId}|${name}` : contractTypeExist.name,
          description: description ? description : name,
        },
      });

      return;
    } catch (error) {
      console.log('Error updating contact type:', error);

      throw error;
    }
  }

  async deleteContractType({
    payload,
    companyId,
  }: {
    payload: DeleteContractTypeDto;
    companyId: string;
  }) {
    const { id } = payload;

    try {
      if (!id) {
        throw new BadRequestException('Contract type Id is required.');
      }

      const contractTypeExist =
        await this.databaseService.contractType.findUnique({
          where: {
            id,
            companyId,
          },
        });

      if (!contractTypeExist) {
        throw new BadRequestException('Contract type not found.');
      }

      await this.databaseService.contractType.update({
        where: {
          id: contractTypeExist.id,
        },
        data: {
          status: 'INACTIVE',
        },
      });

      return;
    } catch (error) {
      console.log('Error deleting designation', error);

      throw error;
    }
  }

  async deleteContractTypeRequest({
    payload,
    token,
  }: {
    payload: DeleteContractTypeDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    try {
      return await this.authorizationRequestMaker.queueRequest({
        payload,
        action: ACTIONS_CONSTANT.delete,
        companyId: decodedToken.companyId,
        module: MODULE_CONSTANT.CONTRACT_TYPE,
        requestedBy: decodedToken.name || decodedToken.email,
      });
    } catch (error) {
      console.log(error);

      throw new Error('Something went wrong. Try again!');
    }
  }

  async updateContractTypeRequest({
    payload,
    token,
  }: {
    payload: UpdateContractTypeDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    try {
      return await this.authorizationRequestMaker.queueRequest({
        payload,
        action: ACTIONS_CONSTANT.update,
        companyId: decodedToken.companyId,
        module: MODULE_CONSTANT.CONTRACT_TYPE,
        requestedBy: decodedToken.name || decodedToken.email,
      });
    } catch (error) {
      console.log(error);

      throw new Error('Something went wrong. Try again!');
    }
  }
}

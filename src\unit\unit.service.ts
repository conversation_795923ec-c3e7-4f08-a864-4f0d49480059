import { BadRequestException, Injectable } from '@nestjs/common';
import { AuthorizationQueue, Region } from '@prisma/client';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { ACTIONS_CONSTANT } from 'src/authorization-queue/constants/entity.constants';
import { MODULE_CONSTANT } from 'src/authorization-queue/constants/module.constant';
import { AuthorizationRequestMaker } from 'src/common/maker/authorization-request.maker';
import { DatabaseService } from 'src/database/database.service';
import { RegionService } from 'src/region/region.service';
import { TaxJurisdictionService } from 'src/tax-jurisdiction/tax-jurisdiction.service';
import { CreateUnitDto } from './dto/create-unit.dto';
import { DeleteUnitDto } from './dto/delete-unit.dto';
import { UpdateUnitDto } from './dto/update-unit.dto';

@Injectable()
export class UnitService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly authTokenService: AuthTokenService,
    private readonly authorizationRequestMaker: AuthorizationRequestMaker,
    private readonly regionService: RegionService,
    private readonly taxJurisdictionService: TaxJurisdictionService,
  ) {}

  async findUnit({ identifier, token }: { identifier: string; token: string }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    const unit = await this.databaseService.unit.findFirst({
      where: {
        OR: [
          { id: identifier },
          {
            name: `${decodedToken.companyId}|${identifier}`,
            companyId: decodedToken.companyId,
          },
        ],
      },
    });

    return unit;
  }
  // Method to find a role by ID or Name
  async getUnits(token: string) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    console.log(decodedToken);

    try {
      const { companyId } = decodedToken;
      const units = await this.databaseService.unit.findMany({
        where: {
          companyId,
        },
        include: {
          taxJurisdiction: {
            select: {
              name: true,
            },
          },
          region: {
            select: {
              name: true,
            },
          },
          _count: {
            select: {
              employee: true,
            },
          },
        },
      });

      return units.map((unit) => ({
        ...unit,
        name: unit.name.split('|')[1],
        taxJurisdiction: unit.taxJurisdiction?.name.split('|')[1],
        region: unit.region?.name.split('|')[1],
      }));
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  // Method to create a new role
  async acceptUnitAction({
    queue,
    approvedBy,
  }: {
    queue: AuthorizationQueue;
    approvedBy: string;
  }) {
    const { requestedBy, action, companyId } = queue;
    switch (action as ACTIONS_CONSTANT) {
      case ACTIONS_CONSTANT.create: {
        let regionExist: Region | null = null;
        const { name, description, taxJurisdiction, region } = JSON.parse(
          queue.data,
        ) as CreateUnitDto;

        if (!taxJurisdiction || !name) {
          throw new BadRequestException('Missing requred field');
        }

        // Check if  region exist
        if (region) {
          regionExist = await this.regionService.findRegionByName(
            `${companyId}|${region}`,
          );

          if (!regionExist) {
            throw new BadRequestException('Region does not exist');
          }
        }

        const taxJurisdictionExist =
          await this.taxJurisdictionService.findTaxJurisdictionByName(
            `${companyId}|${taxJurisdiction}`,
          );

        if (!taxJurisdictionExist) {
          throw new BadRequestException('Tax Jurisdication does not exist');
        }

        // Check if the group already exists by name
        const unitExist = await this.databaseService.unit.findFirst({
          where: {
            companyId,
            name,
          },
        });

        if (unitExist) {
          throw new BadRequestException('Unit already exists');
        }

        await this.databaseService.unit.create({
          data: {
            name: `${companyId}|${name}`,
            description: description || name,
            taxJurisdictionId: taxJurisdictionExist.id,
            regionId: regionExist?.id,
            companyId,
            createdBy: requestedBy,
            approvedBy,
          },
        });

        return true;
      }

      case ACTIONS_CONSTANT.update: {
        const payload = JSON.parse(queue.data) as UpdateUnitDto;

        await this.updateUnit({
          companyId,
          payload,
        });

        return true;
      }
      case ACTIONS_CONSTANT.delete: {
        const payload = JSON.parse(queue.data) as DeleteUnitDto;

        await this.deleteUnit({
          companyId,
          payload,
        });

        return true;
      }

      default:
        return false;
    }
  }

  async createUnit({
    payload,
    token,
  }: {
    payload: CreateUnitDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    const { taxJurisdiction, name, description, region } = payload;

    if (!taxJurisdiction || !name) {
      throw new BadRequestException('Missing required field');
    }

    try {
      await this.databaseService.authorizationQueue.create({
        data: {
          data: JSON.stringify({ taxJurisdiction, name, description, region }),
          action: 'CREATE',
          companyId: decodedToken.companyId,
          module: 'UNIT',
          requestedBy: decodedToken.name,
        },
      });
      return;
    } catch (error) {
      console.log(error);

      throw new Error('Something went wrong. Try again!');
    }
  }

  async updateUnitRequest({
    payload,
    token,
  }: {
    payload: UpdateUnitDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    try {
      return await this.authorizationRequestMaker.queueRequest({
        payload,
        action: ACTIONS_CONSTANT.update,
        companyId: decodedToken.companyId,
        module: MODULE_CONSTANT.UNIT,
        requestedBy: decodedToken.name,
      });
      return;
    } catch (error) {
      console.log(error);

      throw new Error('Something went wrong. Try again!');
    }
  }

  async updateUnit({
    payload,
    companyId,
  }: {
    payload: UpdateUnitDto;
    companyId: string;
  }) {
    const { description, name, id } = payload;

    try {
      if (!id) {
        throw new BadRequestException('Unit Id is required.');
      }

      const unitExist = await this.databaseService.unit.findUnique({
        where: {
          id,
          companyId,
        },
      });

      if (!unitExist) {
        throw new BadRequestException('Unit not found.');
      }

      await this.databaseService.unit.update({
        where: {
          id: unitExist.id,
        },
        data: {
          name: name ? `${companyId}|${name}` : unitExist.name,
          companyId,
          description: description ? description : name,
        },
      });

      return;
    } catch (error) {
      console.log('Error updating unit:', error);

      throw error;
    }
  }

  async deleteUnitRequest({
    payload,
    token,
  }: {
    payload: DeleteUnitDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    try {
      return await this.authorizationRequestMaker.queueRequest({
        payload,
        action: ACTIONS_CONSTANT.delete,
        companyId: decodedToken.companyId,
        module: MODULE_CONSTANT.UNIT,
        requestedBy: decodedToken.name,
      });
    } catch (error) {
      console.log(error);

      throw new Error('Something went wrong. Try again!');
    }
  }

  async deleteUnit({
    payload,
    companyId,
  }: {
    payload: DeleteUnitDto;
    companyId: string;
  }) {
    const { id } = payload;

    try {
      if (!id) {
        throw new BadRequestException('Unit is required.');
      }

      const unitExist = await this.databaseService.unit.findUnique({
        where: {
          id,
          companyId,
        },
      });

      if (!unitExist) {
        throw new BadRequestException('Unit not found.');
      }

      await this.databaseService.unit.update({
        where: {
          id: unitExist.id,
        },
        data: {
          status: 'INACTIVE',
        },
      });

      return;
    } catch (error) {
      console.log('Error deleting unit:', error);

      throw error;
    }
  }
}

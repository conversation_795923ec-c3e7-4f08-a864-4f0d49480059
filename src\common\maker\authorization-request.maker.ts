// src/common/guards/privilege.guard.ts
import { Injectable } from '@nestjs/common';
import { ACTIONS_CONSTANT } from 'src/authorization-queue/constants/entity.constants';
import { MODULE_CONSTANT } from 'src/authorization-queue/constants/module.constant';
import { DatabaseService } from 'src/database/database.service';

@Injectable()
export class AuthorizationRequestMaker {
  constructor(private databaseService: DatabaseService) {}

  async queueRequest({
    payload,
    companyId,
    action,
    module,
    requestedBy,
  }: {
    payload: any;
    companyId: string;
    module: MODULE_CONSTANT;
    action: ACTIONS_CONSTANT;
    requestedBy: string;
  }) {
    try {
      await this.databaseService.authorizationQueue.create({
        data: {
          data: JSON.stringify(payload),
          action,
          module,
          requestedBy,
          company: {
            connect: {
              id: companyId,
            },
          },
        },
      });
      return;
    } catch (error) {
      console.log(error);

      throw new Error('Something went wrong. Try again!');
    }
  }
}

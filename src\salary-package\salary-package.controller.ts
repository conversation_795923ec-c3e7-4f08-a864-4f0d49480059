import { Body, Controller, Get, Post, Req } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { RequirePrivilege } from 'src/common/decorators/require-privilege.decorator';
import { SwaggerService } from 'src/swagger/swagger.service';
import { CreateSalaryPackageDto } from './dto/create-salary-package.dto';
import { SalaryPackageService } from './salary-package.service';

@ApiTags('Salary Package')
@Controller('salary-package')
export class SalaryPackageController {
  constructor(private readonly salaryPackageService: SalaryPackageService) {}
  @SwaggerService.applyOperation({
    method: 'create',
    description: 'Creates a new department with the provided details.',
    dto: CreateSalaryPackageDto,
    statusCodes: [],
  })
  @RequirePrivilege('SALARY_PACKAGE|CREATE')
  @Post('/create')
  createSalaryPackage(
    @Body() payload: CreateSalaryPackageDto,
    @Req() request: Request,
  ) {
    const token = request.headers.authorization?.split(' ')[1];

    return this.salaryPackageService.createSalaryPackage({
      payload,
      token: token!,
    });
  }

  @SwaggerService.applyOperation({
    method: 'get',
    description: 'Get all salary packages.',
    statusCodes: [],
  })
  @RequirePrivilege('SALARY_PACKAGE|VIEW')
  @Get('/read')
  getDepartments(@Req() request: Request) {
    const token = request.headers.authorization?.split(' ')[1];

    return this.salaryPackageService.getSalaryPackages(token!);
  }
}

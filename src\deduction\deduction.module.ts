import { Module } from '@nestjs/common';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { DatabaseModule } from 'src/database/database.module';
import { DeductionController } from './deduction.controller';
import { DeductionService } from './deduction.service';

@Module({
  imports: [DatabaseModule],
  providers: [DeductionService, AuthTokenService],
  controllers: [DeductionController],
})
export class DeductionModule {}

import { Modu<PERSON> } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { jwtConstants } from 'src/authentication/constant';
import { CryptoService } from 'src/common/crypto/crypto.service';
import { DatabaseModule } from 'src/database/database.module';
import { MailService } from 'src/mail/mail.service';
import { OtpService } from 'src/otp/otp.service';
import { AdminAccountService } from './admin-account/admin-account.service';
import { AdminAuthService } from './admin-auth/auth.service';
import { AdminCompanyService } from './admin-company/admin-company.service';
import { AdminRoleService } from './admin-role/admin-role.service';
import { AdminUserService } from './admin-user/admin-user.service';
import { AdminController } from './admin.controller';

@Module({
  imports: [
    DatabaseModule,
    JwtModule.register({
      global: true,
      secret: jwtConstants.secret,
      signOptions: { expiresIn: '1h' },
    }),
  ],
  providers: [
    AuthTokenService,
    AdminAuthService,
    AdminUserService,
    AdminRoleService,
    AdminCompanyService,
    AdminAccountService,
    OtpService,
    CryptoService,
    MailService,
  ],
  controllers: [AdminController],
})
export class AdminModule {}

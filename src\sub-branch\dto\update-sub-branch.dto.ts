import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsNotEmpty, IsString, MaxLength } from 'class-validator';

export class UpdateSubBranchDto {
  @ApiProperty()
  @IsInt()
  @IsNotEmpty()
  id: string;

  @ApiProperty()
  @IsInt()
  @IsNotEmpty()
  branch: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @ApiProperty({ required: false })
  @IsString()
  @MaxLength(255)
  description?: string;
}

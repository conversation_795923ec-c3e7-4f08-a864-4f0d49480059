import { Body, Controller, Post } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { SkipThrottle, Throttle } from '@nestjs/throttler';
import { Public } from 'src/common/decorators/public.decorator';
import { SwaggerService } from 'src/swagger/swagger.service';
import { RequestOtpDto } from './dto/request-otp.dto';
import { VerifyOtpDto } from './dto/verify-otp.dto';
import { OtpService } from './otp.service';

@ApiTags('OTP')
@SkipThrottle()
@Controller('otp')
export class OtpController {
  constructor(private readonly OtpService: OtpService) {}

  @SwaggerService.applyOperation({
    method: 'create',
    description: 'Request OTP',
    dto: RequestOtpDto,
    statusCodes: [],
  })
  @Post('request')
  @Public()
  @Throttle({ short: { ttl: 6000, limit: 1 } }) // Allow 1 requests per minute
  requestOtp(@Body() payload: RequestOtpDto) {
    return this.OtpService.requestOtp(payload);
  }
  @Post('verify')
  @SwaggerService.applyOperation({
    method: 'update',
    description: 'Request OTP',
    dto: VerifyOtpDto,
    statusCodes: [],
  })
  createEntity(@Body() payload: VerifyOtpDto) {
    return this.OtpService.verifyOtp(payload);
  }
}

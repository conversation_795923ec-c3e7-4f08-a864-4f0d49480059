import { Module } from '@nestjs/common';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { DatabaseModule } from 'src/database/database.module';
import { TaxjurisdictionController } from './tax-jurisdiction.controller';
import { TaxJurisdictionService } from './tax-jurisdiction.service';

@Module({
  imports: [DatabaseModule],
  providers: [TaxJurisdictionService, AuthTokenService],
  controllers: [TaxjurisdictionController],
})
export class TaxJurisdictionModule {}

import { BadRequestException, Injectable } from '@nestjs/common';
import { AuthorizationQueue } from '@prisma/client';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { DatabaseService } from 'src/database/database.service';
import { CreateDeductionDto } from './dto/create-deduction.dto';

@Injectable()
export class DeductionService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly authTokenService: AuthTokenService,
  ) {}

  async findDeduction({
    identifier,
    token,
  }: {
    identifier: string;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    const deduction = this.databaseService.deduction.findFirst({
      where: {
        OR: [
          { id: identifier },
          {
            name: identifier,
            companyId: decodedToken.companyId,
          },
        ],
      },
    });

    return deduction;
  }
  // Method to find a role by ID or Name
  async getDeductions(token: string) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    try {
      const { companyId } = decodedToken;
      const deductions = await this.databaseService.deduction.findMany({
        where: {
          companyId,
        },
      });

      return deductions;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async getDeductionsByGradeLevelId({
    token,
    gradeLevelId,
  }: {
    token: string;
    gradeLevelId: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    try {
      const { companyId } = decodedToken;
      const deductions = await this.databaseService.deduction.findMany({
        where: {
          companyId,
          gradeLevelId,
        },
      });

      return deductions;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  // Method to create a new role
  async acceptDeductionAction({
    queue,
    approvedBy,
  }: {
    queue: AuthorizationQueue;
    approvedBy: string;
  }) {
    const { requestedBy, action, companyId } = queue;
    switch (action) {
      case 'CREATE': {
        const { name, description, gradeLevelId, amount } = JSON.parse(
          queue.data,
        ) as CreateDeductionDto;

        // Check if the group already exists by name
        const deductionExist = await this.databaseService.deduction.findUnique({
          where: {
            name_companyId_gradeLevelId: {
              name,
              companyId,
              gradeLevelId,
            },
          },
        });

        if (deductionExist) {
          throw new BadRequestException('Deduction already exists');
        }

        await this.databaseService.deduction.create({
          data: {
            name,
            description: description || name,
            companyId,
            gradeLevelId,
            amount,
            createdBy: requestedBy,
            approvedBy,
          },
        });

        return true;
      }
      default:
        return false;
    }
  }

  async createDeduction({
    payload,
    token,
  }: {
    payload: CreateDeductionDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    try {
      await this.databaseService.authorizationQueue.create({
        data: {
          data: JSON.stringify(payload),
          action: 'CREATE',
          companyId: decodedToken.companyId,
          module: 'DEDUCTION',
          requestedBy: decodedToken.name,
        },
      });
      return;
    } catch (error) {
      console.log(error);

      throw new Error('Something went wrong. Try again!');
    }
  }
}

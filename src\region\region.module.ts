import { Module } from '@nestjs/common';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { DatabaseModule } from 'src/database/database.module';
import { RegionController } from './region.controller';
import { RegionService } from './region.service';

@Module({
  imports: [DatabaseModule],
  providers: [RegionService, AuthTokenService],
  controllers: [RegionController],
})
export class RegionModule {}

import { Body, Controller, Post } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { SkipThrottle, Throttle } from '@nestjs/throttler';
import { Public } from 'src/common/decorators/public.decorator';
import { ResendOtpDto } from 'src/otp/dto/resend-otp.dto';
import { OtpService } from 'src/otp/otp.service';
import { SwaggerService } from 'src/swagger/swagger.service';
import { CompleteEnrollmentDto } from './dto/complete-enrollment.dto';
import { InitiateEnrollmentDto } from './dto/initiate-enrollment.dto';
import { EnrollmentService } from './enrollment.service';

@ApiTags('Enrollment')
@SkipThrottle()
@Controller('enrollment')
@Throttle({ short: { ttl: 60, limit: 5 } }) // Allow 5 requests per minute
export class EnrollmentController {
  constructor(
    private readonly enrollmentService: EnrollmentService,
    private readonly otpService: OtpService,
  ) {}
  @SwaggerService.applyOperation({
    method: 'create',
    description: 'Creates a new company with the provided details.',
    dto: InitiateEnrollmentDto,
    statusCodes: [],
  })
  @Public()
  @Post('initiate')
  initiateEnrollment(@Body() InitiateEnrollmentDto: InitiateEnrollmentDto) {
    return this.enrollmentService.initiateEnrollment(InitiateEnrollmentDto);
  }
  @Public()
  @Post('complete')
  completeEnrollment(@Body() CompleteEnrollmentDto: CompleteEnrollmentDto) {
    return this.enrollmentService.completeEnrollment(CompleteEnrollmentDto);
  }

  @Public()
  @Post('resend-otp')
  resendOtp(@Body() payload: ResendOtpDto) {
    return this.otpService.resendOtp(payload);
  }
}

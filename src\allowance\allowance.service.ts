import { BadRequestException, Injectable } from '@nestjs/common';
import { AuthorizationQueue } from '@prisma/client';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { DatabaseService } from 'src/database/database.service';
import { CreateAllowanceDto } from './dto/create-allowance.dto';

@Injectable()
export class AllowanceService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly authTokenService: AuthTokenService,
  ) {}

  async findAllowance({
    identifier,
    token,
  }: {
    identifier: string;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    const allowance = this.databaseService.allowance.findFirst({
      where: {
        OR: [
          { id: identifier },
          {
            name: identifier,
            companyId: decodedToken.companyId,
          },
        ],
      },
    });

    return allowance;
  }
  // Method to find a role by ID or Name
  async getAllowances(token: string) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    try {
      const { companyId } = decodedToken;
      const allowances = await this.databaseService.allowance.findMany({
        where: {
          companyId,
        },
      });

      return allowances;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async getAllowancesByGradeLevelId({
    token,
    gradeLevelId,
  }: {
    token: string;
    gradeLevelId: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    try {
      const { companyId } = decodedToken;
      const allowances = await this.databaseService.allowance.findMany({
        where: {
          companyId,
          gradeLevelId,
        },
      });

      return allowances;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  // Method to create a new role
  async acceptAllowanceAction({
    queue,
    approvedBy,
  }: {
    queue: AuthorizationQueue;
    approvedBy: string;
  }) {
    const { requestedBy, action, companyId } = queue;
    switch (action) {
      case 'CREATE': {
        const { name, description, gradeLevelId, amount } = JSON.parse(
          queue.data,
        ) as CreateAllowanceDto;

        // Check if the group already exists by name
        const allowanceExist = await this.databaseService.allowance.findUnique({
          where: {
            name_companyId_gradeLevelId: {
              name,
              companyId,
              gradeLevelId,
            },
          },
        });

        if (allowanceExist) {
          throw new BadRequestException('Allowance already exists');
        }

        await this.databaseService.allowance.create({
          data: {
            name,
            description: description || name,
            companyId,
            gradeLevelId,
            amount,
            createdBy: requestedBy,
            approvedBy,
          },
        });

        return true;
      }
      default:
        return false;
    }
  }

  async createAllowance({
    payload,
    token,
  }: {
    payload: CreateAllowanceDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    try {
      await this.databaseService.authorizationQueue.create({
        data: {
          data: JSON.stringify(payload),
          action: 'CREATE',
          companyId: decodedToken.companyId,
          module: 'ALLOWANCE',
          requestedBy: decodedToken.name,
        },
      });
      return;
    } catch (error) {
      console.log(error);

      throw new Error('Something went wrong. Try again!');
    }
  }
}
